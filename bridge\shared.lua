-- Bridge System for ESX and QBCore compatibility
-- Optimized for high player count servers (200+ players)

Framework = {}
Framework.Name = nil
Framework.Object = nil
Framework.Ready = false

-- Performance optimizations
local FrameworkCache = {
    oxInventoryEnabled = nil,
    lastInventoryCheck = 0,
    inventoryCheckInterval = 30000 -- Check every 30 seconds instead of every call
}

-- Optimized framework detection with immediate initialization
local function InitializeFramework()
    local success = false

    if Config.Framework == 'qb' then
        -- Force QBCore
        if GetResourceState('qb-core') == 'started' then
            Framework.Name = 'qb'
            Framework.Object = exports['qb-core']:GetCoreObject()
            success = true
            print('[GM_Airdrop] QBCore framework forced via config')
        else
            print('[GM_Airdrop] QBCore forced but not found! Please install QBCore.')
        end
    elseif Config.Framework == 'esx' then
        -- Force ESX
        if GetResourceState('es_extended') == 'started' then
            Framework.Name = 'esx'
            -- Try export first (faster)
            Framework.Object = exports['es_extended']:getSharedObject()
            if not Framework.Object then
                -- Fallback to event (slower)
                TriggerEvent('esx:getSharedObject', function(obj) Framework.Object = obj end)
            end
            success = true
            print('[GM_Airdrop] ESX framework forced via config')
        else
            print('[GM_Airdrop] ESX forced but not found! Please install ESX.')
        end
    else
        -- Auto-detect framework (prioritize QBCore for performance)
        if GetResourceState('qb-core') == 'started' then
            Framework.Name = 'qb'
            Framework.Object = exports['qb-core']:GetCoreObject()
            success = true
            print('[GM_Airdrop] QBCore framework auto-detected')
        elseif GetResourceState('es_extended') == 'started' then
            Framework.Name = 'esx'
            Framework.Object = exports['es_extended']:getSharedObject()
            if not Framework.Object then
                TriggerEvent('esx:getSharedObject', function(obj) Framework.Object = obj end)
            end
            success = true
            print('[GM_Airdrop] ESX framework auto-detected')
        else
            print('[GM_Airdrop] No supported framework detected! Please install ESX or QBCore.')
        end
    end

    if success and Framework.Object then
        Framework.Ready = true
        -- Cache ox_inventory state on startup
        FrameworkCache.oxInventoryEnabled = GetResourceState('ox_inventory') == 'started'
        FrameworkCache.lastInventoryCheck = GetGameTimer()
    end

    return success
end

-- Initialize immediately instead of polling
InitializeFramework()

-- Optimized utility functions
function IsFrameworkReady()
    return Framework.Ready
end

function GetFrameworkName()
    return Framework.Name
end

function GetFrameworkObject()
    return Framework.Object
end

-- Optimized ox_inventory check with caching
function IsOxInventoryEnabled()
    local currentTime = GetGameTimer()
    if currentTime - FrameworkCache.lastInventoryCheck > FrameworkCache.inventoryCheckInterval then
        FrameworkCache.oxInventoryEnabled = GetResourceState('ox_inventory') == 'started'
        FrameworkCache.lastInventoryCheck = currentTime
    end
    return FrameworkCache.oxInventoryEnabled
end
