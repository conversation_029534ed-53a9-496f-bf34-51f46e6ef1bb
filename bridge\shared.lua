-- Bridge System for ESX and QBCore compatibility
-- Simplified config-based approach - no threads needed

-- Performance optimizations
local FrameworkCache = {
    oxInventoryEnabled = nil,
    lastInventoryCheck = 0,
    inventoryCheckInterval = 30000 -- Check every 30 seconds instead of every call
}

-- Initialize ox_inventory cache immediately
FrameworkCache.oxInventoryEnabled = GetResourceState('ox_inventory') == 'started'
FrameworkCache.lastInventoryCheck = GetGameTimer()

-- Optimized ox_inventory check with caching
function IsOxInventoryEnabled()
    local currentTime = GetGameTimer()
    if currentTime - FrameworkCache.lastInventoryCheck > FrameworkCache.inventoryCheckInterval then
        FrameworkCache.oxInventoryEnabled = GetResourceState('ox_inventory') == 'started'
        FrameworkCache.lastInventoryCheck = currentTime
    end
    return FrameworkCache.oxInventoryEnabled
end

-- Universal notification function - works for both client and server
function ShowNotification(source, message, type, duration)
    duration = duration or 5000

    -- Determine if this is client or server side
    if IsDuplicityVersion() then
        -- Server-side notification
        if Config.Framework == 'qb' or (Config.Framework == 'auto' and GetResourceState('qb-core') == 'started') then
            TriggerClientEvent('QBCore:Notify', source, message, type, duration)
        elseif Config.Framework == 'esx' or (Config.Framework == 'auto' and GetResourceState('es_extended') == 'started') then
            TriggerClientEvent('esx:showNotification', source, message, type, duration)
        else
            -- Fallback to ox_lib
            TriggerClientEvent('ox_lib:notify', source, {
                description = message,
                type = type or 'inform'
            })
        end
    else
        -- Client-side notification
        if Config.Framework == 'qb' or (Config.Framework == 'auto' and GetResourceState('qb-core') == 'started') then
            local QBCore = exports['qb-core']:GetCoreObject()
            if QBCore and QBCore.Functions then
                QBCore.Functions.Notify(message, type, duration)
            end
        elseif Config.Framework == 'esx' or (Config.Framework == 'auto' and GetResourceState('es_extended') == 'started') then
            local ESX = exports['es_extended']:getSharedObject()
            if ESX and ESX.ShowNotification then
                -- Convert QBCore notification types to ESX
                local esxType = 'info'
                if type == 'success' then
                    esxType = 'success'
                elseif type == 'error' then
                    esxType = 'error'
                end
                ESX.ShowNotification(message, esxType, duration)
            end
        else
            -- Fallback to basic notification
            SetNotificationTextEntry('STRING')
            AddTextComponentString(message)
            DrawNotification(false, true)
        end
    end
end
