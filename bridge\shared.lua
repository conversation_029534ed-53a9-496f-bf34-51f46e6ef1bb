-- Bridge System for ESX and QBCore compatibility
-- Shared functions and framework detection

Framework = {}
Framework.Name = nil
Framework.Object = nil

-- Framework Detection
Citizen.CreateThread(function()
    if Config.Framework == 'qb' then
        -- Force QBCore
        if GetResourceState('qb-core') == 'started' then
            Framework.Name = 'qb'
            Framework.Object = exports['qb-core']:GetCoreObject()
            print('[GM_Airdrop] QBCore framework forced via config')
        else
            print('[GM_Airdrop] QBCore forced but not found! Please install QBCore.')
        end
    elseif Config.Framework == 'esx' then
        -- Force ESX
        if GetResourceState('es_extended') == 'started' then
            Framework.Name = 'esx'
            TriggerEvent('esx:getSharedObject', function(obj) Framework.Object = obj end)
            if not Framework.Object then
                Framework.Object = exports['es_extended']:getSharedObject()
            end
            print('[GM_Airdrop] ESX framework forced via config')
        else
            print('[GM_Airdrop] ESX forced but not found! Please install ESX.')
        end
    else
        -- Auto-detect framework
        if GetResourceState('qb-core') == 'started' then
            Framework.Name = 'qb'
            Framework.Object = exports['qb-core']:GetCoreObject()
            print('[GM_Airdrop] QBCore framework auto-detected')
        elseif GetResourceState('es_extended') == 'started' then
            Framework.Name = 'esx'
            TriggerEvent('esx:getSharedObject', function(obj) Framework.Object = obj end)
            if not Framework.Object then
                Framework.Object = exports['es_extended']:getSharedObject()
            end
            print('[GM_Airdrop] ESX framework auto-detected')
        else
            print('[GM_Airdrop] No supported framework detected! Please install ESX or QBCore.')
        end
    end
end)

-- Utility function to check if framework is ready
function IsFrameworkReady()
    return Framework.Object ~= nil
end

-- Get framework name
function GetFrameworkName()
    return Framework.Name
end

-- Get framework object
function GetFrameworkObject()
    return Framework.Object
end
