-- Bridge System for ESX and QBCore compatibility
-- Simplified config-based approach - no threads needed

-- Performance optimizations
local FrameworkCache = {
    oxInventoryEnabled = nil,
    lastInventoryCheck = 0,
    inventoryCheckInterval = 30000 -- Check every 30 seconds instead of every call
}

-- Initialize ox_inventory cache immediately
FrameworkCache.oxInventoryEnabled = GetResourceState('ox_inventory') == 'started'
FrameworkCache.lastInventoryCheck = GetGameTimer()

-- Optimized ox_inventory check with caching
function IsOxInventoryEnabled()
    local currentTime = GetGameTimer()
    if currentTime - FrameworkCache.lastInventoryCheck > FrameworkCache.inventoryCheckInterval then
        FrameworkCache.oxInventoryEnabled = GetResourceState('ox_inventory') == 'started'
        FrameworkCache.lastInventoryCheck = currentTime
    end
    return FrameworkCache.oxInventoryEnabled
end
