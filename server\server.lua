local QBCore = exports['qb-core']:GetCoreObject()
local loc = nil
local looted = false
local lootingPlayer = nil
local cooldown = os.time()

-- Add a function to reset loot state
local function ResetLootState()
    looted = false
    lootingPlayer = nil
end

-- Add a timeout for looting
local function StartLootTimeout()
    Citizen.SetTimeout(Config.Airdrop.LootProgressDuration + 5000, function()
        if looted and lootingPlayer then
            ResetLootState()
        end
    end)
end

RegisterNetEvent("GM_Airdrops:server:sync:loot", function()
    if not looted then
        looted = true
        lootingPlayer = source
        StartLootTimeout()
    end
end)

RegisterNetEvent("GM_Airdrops:server:getLoot", function()
    local src = source
    if not looted or lootingPlayer ~= src then return end
    if #(loc - GetEntityCoords(GetPlayerPed(src))) > 10 then 
        DropPlayer(src, "What are u doing lil bro?") 
        ResetLootState()
        return 
    end
    
    local Player = QBCore.Functions.GetPlayer(src)
    local itemsToGive = math.random(Config.Airdrop.ItemsPerLoot.min, Config.Airdrop.ItemsPerLoot.max)
    for i = 1, itemsToGive, 1 do
        local randItem = Config.LootTable[math.random(1, #Config.LootTable)]
        Player.Functions.AddItem(randItem['item'], randItem['amount'])
        Wait(500)
    end
    
    -- Mark as completed but keep box visible
    looted = true
    lootingPlayer = nil
    
    -- Wait for box despawn time before cleaning up
    Citizen.SetTimeout(Config.Airdrop.BoxDespawnTime, function()
        TriggerClientEvent("GM_Airdrops:client:clearStuff", -1)
        TriggerClientEvent("GM_Airdrops:client:endZone", -1)
        ResetLootState()
    end)
end)

-- Handle player disconnect
AddEventHandler('playerDropped', function()
    if lootingPlayer == source then
        ResetLootState()
    end
end)

lib.callback.register('GM_Airdrops:server:getLootState', function()
    return looted
end)

QBCore.Functions.CreateUseableItem(Config.Airdrop.RequiredItem, function(source, item)
  local phoneitem = exports.ox_inventory:GetItem(source, item, nil, false)
  local playerCount = 0
  for _, playerId in ipairs(GetPlayers()) do
    playerCount += 1
  end
  if os.time() >= phoneitem.metadata.durability then
    return TriggerClientEvent('ox_lib:notify', source, {
      description = 'Your Satellite Phone has 0% battery'
  })
  end
  if playerCount < Config.Airdrop.MinPlayers then
    return TriggerClientEvent('ox_lib:notify', source, {
      description = 'The Russians are unable to accept your request at this time!'
  })
  end
  if os.time() <= cooldown then
    return TriggerClientEvent('ox_lib:notify', source, {
      description = 'The Russians are unable to accept your request at this time!'
  })
  end
  if os.time() < phoneitem.metadata.durability then
    TriggerClientEvent("GM_Airdrops:client:callAirDrop", source, phoneitem)
  end
end)

RegisterNetEvent('GM_Airdrops:server:callAirDrop',function(item)
  local success = exports.ox_inventory:RemoveItem(source, Config.Airdrop.RequiredItem, 1, item.metadata, item.slot, false)
  if success then
    cooldown = os.time() + Config.Airdrop.Cooldown
    looted = false
    --TriggerClientEvent("GM_Airdrops:client:clearStuff", -1)
    local randomloc = math.random(1, #Config.Locations)
    loc = Config.Locations[randomloc]
    TriggerClientEvent("GM_Airdrops:client:startAirdrop", -1, loc)    
  end
end)

RegisterNetEvent("GM_Airdrops:server:cancelLoot", function()
    local src = source
    if lootingPlayer == src then
        ResetLootState()
        TriggerClientEvent('ox_lib:notify', src, {
            description = 'You have cancelled looting the airdrop',
            type = 'inform'
        })
    end
end)