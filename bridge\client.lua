-- Client-side bridge functions for ESX and QBCore compatibility
-- Optimized for high player count servers

Bridge = {}

-- Performance optimizations
local FrameworkObj = nil
local NotificationCache = {}

-- Direct initialization based on config - no threads needed
local function InitializeBridge()
    if Config.Framework == 'qb' or (Config.Framework == 'auto' and GetResourceState('qb-core') == 'started') then
        -- QBCore initialization
        if GetResourceState('qb-core') == 'started' then
            FrameworkObj = exports['qb-core']:GetCoreObject()
            InitializeQBCore()
            print('[GM_Airdrop] QBCore client bridge initialized')
        else
            print('[GM_Airdrop] QBCore not found!')
        end
    elseif Config.Framework == 'esx' or (Config.Framework == 'auto' and GetResourceState('es_extended') == 'started') then
        -- ESX initialization
        if GetResourceState('es_extended') == 'started' then
            FrameworkObj = exports['es_extended']:getSharedObject()
            if not FrameworkObj then
                TriggerEvent('esx:getSharedObject', function(obj) FrameworkObj = obj end)
            end
            InitializeESX()
            print('[GM_Airdrop] ESX client bridge initialized')
        else
            print('[GM_Airdrop] ESX not found!')
        end
    else
        print('[GM_Airdrop] No supported framework detected!')
    end
end

-- Initialize immediately when the script loads
InitializeBridge()

-- QBCore initialization - optimized with cached framework object
function InitializeQBCore()
    if not FrameworkObj or not FrameworkObj.Functions then
        print('[GM_Airdrop] QBCore object not properly initialized')
        return
    end

    -- Notification function
    Bridge.Notify = function(message, type, duration)
        duration = duration or 5000
        FrameworkObj.Functions.Notify(message, type, duration)
    end

    -- Get player data
    Bridge.GetPlayerData = function()
        return FrameworkObj.Functions.GetPlayerData()
    end

    -- Check if player is loaded
    Bridge.IsPlayerLoaded = function()
        local playerData = Bridge.GetPlayerData()
        return playerData and playerData.citizenid ~= nil
    end
end

-- ESX initialization - optimized with cached framework object
function InitializeESX()
    if not FrameworkObj or not FrameworkObj.ShowNotification then
        print('[GM_Airdrop] ESX object not properly initialized')
        return
    end

    -- Pre-compute notification type mapping for performance
    local notificationTypeMap = {
        success = 'success',
        error = 'error',
        primary = 'info',
        info = 'info'
    }

    -- Notification function
    Bridge.Notify = function(message, type, duration)
        duration = duration or 5000
        local esxType = notificationTypeMap[type] or 'info'
        FrameworkObj.ShowNotification(message, esxType, duration)
    end

    -- Get player data
    Bridge.GetPlayerData = function()
        return FrameworkObj.GetPlayerData()
    end

    -- Check if player is loaded
    Bridge.IsPlayerLoaded = function()
        local playerData = Bridge.GetPlayerData()
        return playerData and playerData.identifier ~= nil
    end
end

-- Universal notification function with fallback
function ShowNotification(message, type, duration)
    if Bridge.Notify then
        Bridge.Notify(message, type, duration)
    else
        -- Fallback to basic notification
        SetNotificationTextEntry('STRING')
        AddTextComponentString(message)
        DrawNotification(false, true)
    end
end

-- Export functions for easy access
exports('ShowNotification', ShowNotification)
exports('GetPlayerData', function() return Bridge.GetPlayerData and Bridge.GetPlayerData() or nil end)
exports('IsPlayerLoaded', function() return Bridge.IsPlayerLoaded and Bridge.IsPlayerLoaded() or false end)
