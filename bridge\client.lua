-- Client-side bridge functions for ESX and QBCore compatibility
-- Optimized for high player count servers

Bridge = {}

-- Performance optimizations
local FrameworkObj = nil
local NotificationCache = {}

-- Optimized initialization - no polling loop
Citizen.CreateThread(function()
    -- Framework should be ready immediately from shared.lua
    if IsFrameworkReady() then
        FrameworkObj = GetFrameworkObject() -- Cache the framework object

        if GetFrameworkName() == 'qb' then
            InitializeQBCore()
        elseif GetFrameworkName() == 'esx' then
            InitializeESX()
        end
    else
        -- Fallback with timeout instead of infinite loop
        local attempts = 0
        local maxAttempts = 50 -- 5 seconds max wait

        while not IsFrameworkReady() and attempts < maxAttempts do
            Citizen.Wait(100)
            attempts = attempts + 1
        end

        if IsFrameworkReady() then
            FrameworkObj = GetFrameworkObject()

            if GetFrameworkName() == 'qb' then
                InitializeQBCore()
            elseif GetFrameworkName() == 'esx' then
                InitializeESX()
            end
        else
            print('[GM_Airdrop] Client bridge initialization failed - framework not ready')
        end
    end
end)

-- QBCore initialization - optimized with cached framework object
function InitializeQBCore()
    print('[GM_Airdrop] Initializing QBCore client bridge')

    -- Cache framework object for better performance
    FrameworkObj = FrameworkObj or GetFrameworkObject()

    -- Notification function
    Bridge.Notify = function(message, type, duration)
        duration = duration or 5000
        FrameworkObj.Functions.Notify(message, type, duration)
    end

    -- Get player data
    Bridge.GetPlayerData = function()
        return FrameworkObj.Functions.GetPlayerData()
    end

    -- Check if player is loaded
    Bridge.IsPlayerLoaded = function()
        local playerData = Bridge.GetPlayerData()
        return playerData and playerData.citizenid ~= nil
    end
end

-- ESX initialization - optimized with cached framework object
function InitializeESX()
    print('[GM_Airdrop] Initializing ESX client bridge')

    -- Cache framework object for better performance
    FrameworkObj = FrameworkObj or GetFrameworkObject()

    -- Pre-compute notification type mapping for performance
    local notificationTypeMap = {
        success = 'success',
        error = 'error',
        primary = 'info',
        info = 'info'
    }

    -- Notification function
    Bridge.Notify = function(message, type, duration)
        duration = duration or 5000
        local esxType = notificationTypeMap[type] or 'info'
        FrameworkObj.ShowNotification(message, esxType, duration)
    end

    -- Get player data
    Bridge.GetPlayerData = function()
        return FrameworkObj.GetPlayerData()
    end

    -- Check if player is loaded
    Bridge.IsPlayerLoaded = function()
        local playerData = Bridge.GetPlayerData()
        return playerData and playerData.identifier ~= nil
    end
end

-- Universal notification function with fallback
function ShowNotification(message, type, duration)
    if Bridge.Notify then
        Bridge.Notify(message, type, duration)
    else
        -- Fallback to basic notification
        SetNotificationTextEntry('STRING')
        AddTextComponentString(message)
        DrawNotification(false, true)
    end
end

-- Export functions for easy access
exports('ShowNotification', ShowNotification)
exports('GetPlayerData', function() return Bridge.GetPlayerData and Bridge.GetPlayerData() or nil end)
exports('IsPlayerLoaded', function() return Bridge.IsPlayerLoaded and Bridge.IsPlayerLoaded() or false end)
