-- Client-side bridge functions for ESX and QBCore compatibility

Bridge = {}

-- Wait for framework to be ready
Citizen.CreateThread(function()
    while not IsFrameworkReady() do
        Citizen.Wait(100)
    end
    
    -- Initialize framework-specific functions
    if GetFrameworkName() == 'qb' then
        InitializeQBCore()
    elseif GetFrameworkName() == 'esx' then
        InitializeESX()
    end
end)

-- QBCore initialization
function InitializeQBCore()
    print('[GM_Airdrop] Initializing QBCore client bridge')
    
    -- Notification function
    Bridge.Notify = function(message, type, duration)
        duration = duration or 5000
        GetFrameworkObject().Functions.Notify(message, type, duration)
    end
    
    -- Get player data
    Bridge.GetPlayerData = function()
        return GetFrameworkObject().Functions.GetPlayerData()
    end
    
    -- Check if player is loaded
    Bridge.IsPlayerLoaded = function()
        local playerData = Bridge.GetPlayerData()
        return playerData and playerData.citizenid ~= nil
    end
end

-- ESX initialization
function InitializeESX()
    print('[GM_Airdrop] Initializing ESX client bridge')
    
    -- Notification function
    Bridge.Notify = function(message, type, duration)
        duration = duration or 5000
        -- Convert QBCore notification types to ESX
        local esxType = 'info'
        if type == 'success' then
            esxType = 'success'
        elseif type == 'error' then
            esxType = 'error'
        elseif type == 'primary' then
            esxType = 'info'
        end
        
        GetFrameworkObject().ShowNotification(message, esxType, duration)
    end
    
    -- Get player data
    Bridge.GetPlayerData = function()
        return GetFrameworkObject().GetPlayerData()
    end
    
    -- Check if player is loaded
    Bridge.IsPlayerLoaded = function()
        local playerData = Bridge.GetPlayerData()
        return playerData and playerData.identifier ~= nil
    end
end

-- Universal notification function with fallback
function ShowNotification(message, type, duration)
    if Bridge.Notify then
        Bridge.Notify(message, type, duration)
    else
        -- Fallback to basic notification
        SetNotificationTextEntry('STRING')
        AddTextComponentString(message)
        DrawNotification(0, 1)
    end
end

-- Export functions for easy access
exports('ShowNotification', ShowNotification)
exports('GetPlayerData', function() return Bridge.GetPlayerData and Bridge.GetPlayerData() or nil end)
exports('IsPlayerLoaded', function() return Bridge.IsPlayerLoaded and Bridge.IsPlayerLoaded() or false end)
