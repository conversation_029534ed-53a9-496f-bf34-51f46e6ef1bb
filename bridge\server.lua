-- Server-side bridge functions for ESX and QBCore compatibility

Bridge = {}

-- Wait for framework to be ready
Citizen.CreateThread(function()
    while not IsFrameworkReady() do
        Citizen.Wait(100)
    end
    
    -- Initialize framework-specific functions
    if GetFrameworkName() == 'qb' then
        InitializeQBCore()
    elseif GetFrameworkName() == 'esx' then
        InitializeESX()
    end
end)

-- QBCore initialization
function InitializeQBCore()
    print('[GM_Airdrop] Initializing QBCore server bridge')
    
    -- Get player function
    Bridge.GetPlayer = function(source)
        return GetFrameworkObject().Functions.GetPlayer(source)
    end
    
    -- Add item to player
    Bridge.AddItem = function(source, item, amount, metadata)
        if GetResourceState('ox_inventory') == 'started' and exports.ox_inventory then
            return exports.ox_inventory:AddItem(source, item, amount, metadata)
        else
            local Player = Bridge.GetPlayer(source)
            if Player then
                return Player.Functions.AddItem(item, amount, false, metadata)
            end
        end
        return false
    end

    -- Remove item from player
    Bridge.RemoveItem = function(source, item, amount, metadata, slot)
        if GetResourceState('ox_inventory') == 'started' and exports.ox_inventory then
            return exports.ox_inventory:RemoveItem(source, item, amount, metadata, slot, false)
        else
            local Player = Bridge.GetPlayer(source)
            if Player then
                return Player.Functions.RemoveItem(item, amount, slot)
            end
        end
        return false
    end

    -- Create useable item
    Bridge.CreateUseableItem = function(item, callback)
        GetFrameworkObject().Functions.CreateUseableItem(item, callback)
    end

    -- Get item from inventory
    Bridge.GetItem = function(source, item, metadata, slot)
        if GetResourceState('ox_inventory') == 'started' and exports.ox_inventory then
            return exports.ox_inventory:GetItem(source, item, metadata, slot)
        else
            local Player = Bridge.GetPlayer(source)
            if Player then
                return Player.Functions.GetItemByName(item)
            end
        end
        return nil
    end

    -- Send notification to client
    Bridge.Notify = function(source, message, type, duration)
        TriggerClientEvent('QBCore:Notify', source, message, type, duration)
    end
end

-- ESX initialization
function InitializeESX()
    print('[GM_Airdrop] Initializing ESX server bridge')

    -- Get player function
    Bridge.GetPlayer = function(source)
        return GetFrameworkObject().GetPlayerFromId(source)
    end

    -- Add item to player
    Bridge.AddItem = function(source, item, amount, metadata)
        if GetResourceState('ox_inventory') == 'started' and exports.ox_inventory then
            return exports.ox_inventory:AddItem(source, item, amount, metadata)
        else
            local xPlayer = Bridge.GetPlayer(source)
            if xPlayer then
                xPlayer.addInventoryItem(item, amount)
                return true
            end
        end
        return false
    end

    -- Remove item from player
    Bridge.RemoveItem = function(source, item, amount, metadata, slot)
        if GetResourceState('ox_inventory') == 'started' and exports.ox_inventory then
            return exports.ox_inventory:RemoveItem(source, item, amount, metadata, slot, false)
        else
            local xPlayer = Bridge.GetPlayer(source)
            if xPlayer then
                xPlayer.removeInventoryItem(item, amount)
                return true
            end
        end
        return false
    end

    -- Create useable item
    Bridge.CreateUseableItem = function(item, callback)
        GetFrameworkObject().RegisterUsableItem(item, callback)
    end

    -- Get item from inventory
    Bridge.GetItem = function(source, item, metadata, slot)
        if GetResourceState('ox_inventory') == 'started' and exports.ox_inventory then
            return exports.ox_inventory:GetItem(source, item, metadata, slot)
        else
            local xPlayer = Bridge.GetPlayer(source)
            if xPlayer then
                return xPlayer.getInventoryItem(item)
            end
        end
        return nil
    end

    -- Send notification to client
    Bridge.Notify = function(source, message, type, duration)
        local xPlayer = Bridge.GetPlayer(source)
        if xPlayer then
            xPlayer.showNotification(message, type, duration)
        end
    end
end

-- Universal functions with fallbacks
function GetPlayer(source)
    return Bridge.GetPlayer and Bridge.GetPlayer(source) or nil
end

function AddItem(source, item, amount, metadata)
    return Bridge.AddItem and Bridge.AddItem(source, item, amount, metadata) or false
end

function RemoveItem(source, item, amount, metadata, slot)
    return Bridge.RemoveItem and Bridge.RemoveItem(source, item, amount, metadata, slot) or false
end

function CreateUseableItem(item, callback)
    if Bridge.CreateUseableItem then
        Bridge.CreateUseableItem(item, callback)
    end
end

function GetItem(source, item, metadata, slot)
    return Bridge.GetItem and Bridge.GetItem(source, item, metadata, slot) or nil
end

function NotifyPlayer(source, message, type, duration)
    if Bridge.Notify then
        Bridge.Notify(source, message, type, duration)
    else
        TriggerClientEvent('ox_lib:notify', source, {
            description = message,
            type = type or 'inform'
        })
    end
end

-- Export functions for easy access
exports('GetPlayer', GetPlayer)
exports('AddItem', AddItem)
exports('RemoveItem', RemoveItem)
exports('CreateUseableItem', CreateUseableItem)
exports('GetItem', GetItem)
exports('NotifyPlayer', NotifyPlayer)
