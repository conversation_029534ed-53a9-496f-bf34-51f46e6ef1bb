# GM Airdrop - Installation Guide

## Quick Setup

### For QBCore Servers
1. Place the resource in your `resources` folder
2. Add `ensure GM_Airdrop` to your `server.cfg`
3. Restart your server
4. The script will automatically detect QBCore

### For ESX Servers
1. Place the resource in your `resources` folder
2. Add `ensure GM_Airdrop` to your `server.cfg`
3. Restart your server
4. The script will automatically detect ESX

### Manual Framework Selection (Optional)
If you want to force a specific framework, edit `config.lua`:

```lua
-- Set to 'qb' for QBCore, 'esx' for ESX, or 'auto' for automatic detection
Config.Framework = 'auto'  -- Change this if needed
```

## Dependencies

### Required Dependencies
- **ox_lib** - UI library
- **ox_target** - Interaction system
- **PolyZone** - Zone management
- Either **qb-core** OR **es_extended**

### Optional Dependencies
- **ox_inventory** - Enhanced inventory system (recommended)

## Database Setup

### For ESX Users
Make sure your items exist in your `items` table. The default loot items are:
- WEAPON_AK47
- WEAPON_HK416
- WEAPON_MP5
- WEAPON_GLOCK18C
- WEAPON_REMINGTON

### For QBCore Users
Make sure your items exist in your `qb-core/shared/items.lua`. The default loot items should already be there.

## Item Setup

The script uses a satellite phone item (`xd_satellite_phone`) to call airdrops. Make sure this item exists in your inventory system.

### QBCore Item Example
Add to `qb-core/shared/items.lua`:
```lua
['xd_satellite_phone'] = {
    ['name'] = 'xd_satellite_phone',
    ['label'] = 'Satellite Phone',
    ['weight'] = 500,
    ['type'] = 'item',
    ['image'] = 'satellite_phone.png',
    ['unique'] = true,
    ['useable'] = true,
    ['shouldClose'] = true,
    ['combinable'] = nil,
    ['description'] = 'A military-grade satellite phone used to call in airdrops'
},
```

### ESX Item Example
Add to your items database:
```sql
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
('xd_satellite_phone', 'Satellite Phone', 1, 0, 1);
```

## Configuration

Edit `config.lua` to customize:
- Airdrop locations
- Loot table
- Cooldown times
- Player requirements
- Animation settings

## Verification

After installation, check your server console for:
- `[GM_Airdrop] QBCore framework auto-detected` (for QBCore)
- `[GM_Airdrop] ESX framework auto-detected` (for ESX)

If you see `[GM_Airdrop] No supported framework detected!`, check your framework installation.

## Troubleshooting

### Common Issues

1. **Framework not detected**
   - Ensure your framework starts before GM_Airdrop
   - Check resource names: `qb-core` or `es_extended`

2. **Items not working**
   - Verify items exist in your database/shared files
   - Check item names match exactly

3. **Notifications not showing**
   - Ensure ox_lib is installed and working
   - Check framework-specific notification systems

4. **Inventory issues**
   - If using ox_inventory, ensure it's properly configured
   - Check player inventory space

### Getting Help

1. Check server console for error messages
2. Verify all dependencies are installed
3. Test with default configuration first
4. Check the BRIDGE_README.md for detailed information

## Performance Notes

- The bridge system adds minimal overhead
- Framework detection happens once at startup
- All functions are cached for optimal performance
