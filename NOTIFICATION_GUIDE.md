# GM Airdrop - Notification System Guide

## 🔔 Configurable Notification System

The GM Airdrop script now features a fully configurable notification system that supports multiple notification libraries and frameworks.

## ⚙️ Configuration Options

### **Framework Integration**
```lua
Config.Notifications = {
    -- Client-side notifications
    Client = 'framework', -- Options: 'framework', 'ox_lib', 'native', 'mythic', 'okoknotify'
    
    -- Server-side notifications  
    Server = 'framework', -- Options: 'framework', 'ox_lib', 'mythic', 'okoknotify'
}
```

## 📋 Supported Notification Systems

### **1. Framework Native (`'framework'`)**
- **QBCore**: Uses `QBCore:Notify` event
- **ESX**: Uses `esx:showNotification` event
- **Automatic**: Detects your framework automatically
- **Best for**: Servers wanting consistent framework integration

### **2. ox_lib (`'ox_lib'`)**
- **Modern**: Clean, customizable notifications
- **Features**: Positioning, icons, colors, animations
- **Dependency**: Requires ox_lib
- **Best for**: Modern servers with ox_lib ecosystem

### **3. Native GTA (`'native'`)**
- **Basic**: Uses GTA's built-in notification system
- **Reliable**: Always works, no dependencies
- **Simple**: Basic text notifications
- **Best for**: Minimal setups or fallback option

### **4. Mythic Notify (`'mythic'`)**
- **Popular**: Widely used notification system
- **Dependency**: Requires mythic_notify resource
- **Features**: Multiple positions, animations
- **Best for**: Servers already using mythic_notify

### **5. okokNotify (`'okoknotify'`)**
- **Advanced**: Feature-rich notification system
- **Dependency**: Requires okokNotify resource
- **Features**: Custom styling, sounds, positioning
- **Best for**: Servers wanting advanced notifications

## 🎨 Customization Options

### **Positioning**
```lua
Config.Notifications = {
    Position = 'top-right', -- Available positions:
    -- 'top', 'top-right', 'top-left'
    -- 'bottom', 'bottom-right', 'bottom-left'
    -- 'center'
}
```

### **Styling Settings**
```lua
Config.Notifications = {
    Settings = {
        ShowIcons = true,           -- Show notification icons
        DefaultDuration = 5000,     -- Default duration (ms)
        PlaySound = true,           -- Enable sound effects
        
        -- Custom colors (hex format)
        Colors = {
            success = '#10B981',    -- Green
            error = '#EF4444',      -- Red  
            info = '#3B82F6',       -- Blue
            warning = '#F59E0B'     -- Orange
        }
    }
}
```

## 🔧 Usage Examples

### **Different Configurations**

#### **Modern Setup (ox_lib)**
```lua
Config.Notifications = {
    Client = 'ox_lib',
    Server = 'ox_lib',
    Position = 'top-right',
    Settings = {
        ShowIcons = true,
        DefaultDuration = 4000,
        PlaySound = true
    }
}
```

#### **Framework Integration**
```lua
Config.Notifications = {
    Client = 'framework',  -- Uses QBCore/ESX notifications
    Server = 'framework',
    Settings = {
        DefaultDuration = 5000
    }
}
```

#### **Mixed Setup**
```lua
Config.Notifications = {
    Client = 'mythic',     -- Client uses mythic_notify
    Server = 'ox_lib',     -- Server uses ox_lib
    Position = 'bottom'
}
```

#### **Fallback Setup**
```lua
Config.Notifications = {
    Client = 'native',     -- Always works
    Server = 'framework',  -- Framework integration
    Settings = {
        DefaultDuration = 3000
    }
}
```

## 🚀 Performance Considerations

### **Best Performance**
1. **native** - Fastest, no dependencies
2. **framework** - Good performance, framework integrated
3. **ox_lib** - Modern with good performance
4. **mythic/okoknotify** - Feature-rich but slightly heavier

### **Recommendations by Server Size**

#### **Small Servers (< 50 players)**
- Any notification system works well
- Choose based on features needed

#### **Medium Servers (50-100 players)**
- **ox_lib** or **framework** recommended
- Good balance of features and performance

#### **Large Servers (100+ players)**
- **framework** or **native** recommended
- Prioritize performance over advanced features

#### **Enterprise Servers (200+ players)**
- **native** for maximum performance
- **framework** for integration benefits

## 🔍 Troubleshooting

### **Notifications Not Showing**

1. **Check Dependencies**
   ```lua
   -- Make sure required resources are started
   ensure ox_lib          -- For ox_lib notifications
   ensure mythic_notify   -- For mythic notifications
   ensure okokNotify      -- For okokNotify notifications
   ```

2. **Check Configuration**
   ```lua
   -- Verify your config matches your setup
   Config.Notifications.Client = 'your_choice'
   Config.Notifications.Server = 'your_choice'
   ```

3. **Test Fallback**
   ```lua
   -- Try native notifications as test
   Config.Notifications.Client = 'native'
   ```

### **Framework Notifications Not Working**

1. **Verify Framework**
   ```lua
   -- Check framework detection
   Config.Framework = 'auto' -- or 'qb'/'esx'
   ```

2. **Check Resource Order**
   ```cfg
   # In server.cfg - framework must start first
   ensure qb-core     # or es_extended
   ensure GM_Airdrop
   ```

## 📊 Notification Types

The system supports these notification types:
- **success** - Green notifications for positive actions
- **error** - Red notifications for errors/failures  
- **info/primary** - Blue notifications for information
- **warning** - Orange notifications for warnings

## 🎯 Best Practices

1. **Match Your Server Style** - Choose notifications that fit your server's UI
2. **Consider Dependencies** - Fewer dependencies = more reliable
3. **Test Thoroughly** - Test with your specific setup
4. **Have Fallbacks** - Native notifications always work
5. **Performance First** - For large servers, prioritize performance

The configurable notification system gives you complete control over how players receive airdrop notifications while maintaining compatibility across all major frameworks and notification systems!
