# GM Airdrop - Framework Bridge System

This script now supports both **ESX** and **QBCore** frameworks through a comprehensive bridge system.

## Features

- **Automatic Framework Detection**: The script automatically detects which framework is running
- **Manual Framework Selection**: You can force a specific framework in the config
- **Universal Functions**: All framework-specific functions are abstracted through the bridge
- **Seamless Integration**: No changes needed to your existing framework setup

## Installation

1. Ensure you have either ESX or QBCore installed and running
2. Place the GM_Airdrop resource in your resources folder
3. Add `ensure GM_Airdrop` to your server.cfg
4. Configure the framework setting in `config.lua` if needed

## Configuration

### Framework Selection

In `config.lua`, you can set the framework preference:

```lua
-- Framework Configuration
-- Set to 'auto' for automatic detection, 'qb' for QBCore, or 'esx' for ESX
Config.Framework = 'auto'
```

**Options:**
- `'auto'` - Automatically detects the framework (recommended)
- `'qb'` - Forces QBCore framework
- `'esx'` - Forces ESX framework

### Inventory System

The bridge system supports both:
- **ox_inventory** (recommended for both frameworks)
- **Native framework inventories** (qb-inventory, esx_inventory)

The script will automatically detect and use ox_inventory if available, otherwise it falls back to the native framework inventory.

## Bridge System Components

### Shared Bridge (`bridge/shared.lua`)
- Framework detection and initialization
- Common utility functions
- Framework object management

### Client Bridge (`bridge/client.lua`)
- Client-side notification system
- Player data management
- Framework-specific client functions

### Server Bridge (`bridge/server.lua`)
- Player management functions
- Inventory operations (add/remove items)
- Useable item registration
- Server-side notifications

## Supported Functions

### Notifications
- **QBCore**: Uses `QBCore.Functions.Notify()`
- **ESX**: Uses `ESX.ShowNotification()`
- **Fallback**: Uses ox_lib notifications

### Player Management
- **QBCore**: `QBCore.Functions.GetPlayer()`
- **ESX**: `ESX.GetPlayerFromId()`

### Inventory Operations
- **ox_inventory**: Direct exports (preferred)
- **QBCore**: Player.Functions.AddItem/RemoveItem
- **ESX**: xPlayer.addInventoryItem/removeInventoryItem

### Useable Items
- **QBCore**: `QBCore.Functions.CreateUseableItem()`
- **ESX**: `ESX.RegisterUsableItem()`

## Dependencies

### Required
- **ox_lib** - For UI components and utilities
- **ox_target** - For interaction system
- **PolyZone** - For zone management
- Either **qb-core** OR **es_extended**

### Optional but Recommended
- **ox_inventory** - For enhanced inventory management

## Troubleshooting

### Framework Not Detected
1. Check that your framework resource is started before GM_Airdrop
2. Verify the framework resource name matches the expected names:
   - QBCore: `qb-core`
   - ESX: `es_extended`
3. Check server console for framework detection messages

### Inventory Issues
1. Ensure ox_inventory is properly configured if using it
2. Check that item names in the loot table exist in your items database
3. Verify player has inventory space for loot items

### Notification Issues
1. Framework notifications not working? The script falls back to ox_lib notifications
2. Ensure ox_lib is properly installed and configured

## Console Messages

The bridge system provides helpful console messages:
- `[GM_Airdrop] QBCore framework auto-detected`
- `[GM_Airdrop] ESX framework auto-detected`
- `[GM_Airdrop] No supported framework detected!`

## Support

If you encounter issues:
1. Check the server console for error messages
2. Verify all dependencies are installed and updated
3. Ensure your framework is properly configured
4. Test with `Config.Framework = 'auto'` first

## Migration from QBCore-only Version

If you're upgrading from a QBCore-only version:
1. The script will continue to work with QBCore without any changes
2. All existing functionality is preserved
3. You can now also use it with ESX servers

The bridge system is fully backward compatible with existing QBCore installations.
