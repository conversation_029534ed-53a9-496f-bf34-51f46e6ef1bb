fx_version 'cerulean'
game 'gta5'

author 'GM'
version '1.0'

lua54 'yes'

shared_scripts {
  '@ox_lib/init.lua',
  'config.lua',
  'bridge/shared.lua'
}

client_scripts {
  'bridge/client.lua',
  'client/*.lua',
  '@PolyZone/client.lua',
  '@PolyZone/BoxZone.lua',
  '@PolyZone/EntityZone.lua',
  '@PolyZone/CircleZone.lua',
  '@PolyZone/ComboZone.lua',
}

server_scripts {
  'bridge/server.lua',
  'server/*.lua'
}

escrow_ignore {
  "config.lua",
}