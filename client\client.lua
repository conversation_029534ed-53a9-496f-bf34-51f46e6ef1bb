local QBCore = exports['qb-core']:GetCoreObject()

local airdropBlip = nil
local radius = nil
local Plane = nil
local Pilot = nil
local planeblip = nil
local effect = nil
local drop = nil
local hasNotified = false

RegisterNetEvent('GM_Airdrops:client:startAirdrop',function(coords)
  QBCore.Functions.Notify("Airdrop Inbound!", 30000)
  PlaySoundFrontend(-1, 'Mission_Pass_Notify', 'DLC_HEISTS_GENERAL_FRONTEND_SOUNDS', false)
  airdropBlip = AddBlipForCoord(coords.x, coords.y, coords.z)
  SetBlipSprite(airdropBlip, Config.Zone.BlipSprite)
  SetBlipDisplay(airdropBlip, 4)
  SetBlipScale(airdropBlip, 1.0)
  SetBlipAsShortRange(airdropBlip, true)
  SetBlipColour(airdropBlip, Config.Zone.BlipColor)
  BeginTextCommandSetBlipName("STRING")
  AddTextComponentSubstringPlayerName("Air Drop")
  EndTextCommandSetBlipName(airdropBlip)

  lib.requestNamedPtfxAsset("scr_biolab_heist")
  SetPtfxAssetNextCall("scr_biolab_heist")
  effect = StartParticleFxLoopedAtCoord("scr_heist_biolab_flare", coords, 0.0, 0.0, 0.0, 1.0, false, false, false, false)


  local radiusBlipSize = Config.Zone.Radius
  radius = AddBlipForRadius(coords, radiusBlipSize)
  SetBlipColour(radius, Config.Zone.BlipColor)
  SetBlipAlpha(radius, Config.Zone.BlipAlpha)

  AirDropZone = CircleZone:Create(vector3(coords.x, coords.y, coords.z), radiusBlipSize, {
    name="airdrop_circle_zone",
    debugPoly=false,
  })
  Citizen.CreateThread(function()
    while true do
      Wait(1000)
      if AirDropZone.destroyed then
        break
      end
      AirDropZone:onPlayerInOut(function(isPointInside)
        if isPointInside then
          if Config.Airdrop.HaltVehilce then
            if IsPedInAnyVehicle(PlayerPedId(), false) then
              local VehicleToStop = GetVehiclePedIsIn(PlayerPedId())
              BringVehicleToHalt(VehicleToStop, 2, 2, true)
            end
          end
          if Config.ProximityNotifications.Enabled and not hasNotified then
            QBCore.Functions.Notify(Config.ProximityNotifications.Messages.Nearby, "success")
            hasNotified = true
          end
        else
          if Config.ProximityNotifications.Enabled and hasNotified then
            QBCore.Functions.Notify(Config.ProximityNotifications.Messages.Leaving, "error")
            hasNotified = false
          end
        end
      end)
    end
  end)

  spawnAirPlane(coords)
end)


function spawnAirPlane(coords)
  Wait(Config.Airdrop.PlaneSpawnDelay)
  local dropped = false

  lib.requestModel(Config.Aircraft.Models.Vehicle)
  lib.requestModel(Config.Aircraft.Models.Pilot)

  Plane = CreateVehicle(GetHashKey(Config.Aircraft.Models.Vehicle), Config.Aircraft.Settings.SpawnPoint.x, Config.Aircraft.Settings.SpawnPoint.y, Config.Aircraft.Settings.SpawnPoint.z, heading, false, true)
	Pilot = CreatePed(4, GetHashKey(Config.Aircraft.Models.Pilot), Config.Aircraft.Settings.SpawnPoint.x, Config.Aircraft.Settings.SpawnPoint.y, Config.Aircraft.Settings.SpawnPoint.z, heading, false, true)

  lib.waitFor(function()
		if DoesEntityExist(Plane) and DoesEntityExist(Pilot) then
			return true
		end
	end, "entity does not exist")

  planeblip = AddBlipForEntity(Plane)
  SetBlipSprite(planeblip,307)
	SetBlipRotation(planeblip,GetEntityHeading(Pilot))
  SetPedIntoVehicle(Pilot, Plane, -1)

	ControlLandingGear(Plane, 3)
	SetVehicleEngineOn(Plane, true, true, false)
  SetEntityVelocity(Plane, 0.9 * Config.Aircraft.Settings.Speed, 0.9 * Config.Aircraft.Settings.Speed, 0.0)

  while DoesEntityExist(Plane) do
    if not NetworkHasControlOfEntity(Plane) then
			NetworkRequestControlOfEntity(Plane)
			Wait(10)
		end

    SetBlipRotation(planeblip, Ceil(GetEntityHeading(Plane)))
    if not dropped then
      TaskPlaneMission(Pilot, Plane, 0, 0, coords.x, coords.y, coords.z + 250, 6, 0, 0, GetEntityHeading(Pilot), 3000.0, 500.0)
    end
		local activeCoords = GetEntityCoords(Plane)
    local dist = #(activeCoords - coords)
    if dist < 300 or dropped then
      Wait(1000)
      TaskPlaneMission(Pilot, Plane, 0, 0, -2194.32, 5120.9, Config.Aircraft.Settings.Height, 6, 0, 0, GetEntityHeading(Pilot), 3000.0, 500.0)
      if not dropped then
        spawnCrate(coords)
        dropped = true
      end
      if dist > 2000 then 
        DeleteEntity(Plane)
        DeleteEntity(Pilot)
        Plane = nil
        Pilot = nil
        dropped = false
        break
      end
    end

    Wait(1000)
  end



end


function spawnCrate(coords)
  lib.requestModel(Config.Airdrop.CrateModel)
  drop = CreateObject(Config.Airdrop.CrateModel, coords.x, coords.y, coords.z + 200, false, true)

  lib.waitFor(function()
		if DoesEntityExist(drop) then
			return true
		end
	end, "entity does not exist")
  SetObjectPhysicsParams(drop,80000.0, 0.1, 0.0, 0.0, 0.0, 700.0, 0.0, 0.0, 0.0, 0.1, 0.0)
	SetEntityLodDist(drop, 1000)
	ActivatePhysics(drop)
	SetDamping(drop, 2, 0.1)
	SetEntityVelocity(drop, 0.0, 0.0, -7000.0)

  exports.ox_target:addLocalEntity(drop, {{
    name = 'airdrop_box',
    icon = 'fa-solid fa-user-secret',
    label = "Loot Supplies",
    distance = 1.5,
    debug = false,
    onSelect = function()
      local state = lib.callback.await('GM_Airdrops:server:getLootState', false)
      if not state then
        TriggerServerEvent("GM_Airdrops:server:sync:loot")
        if lib.progressBar({
          label = "Looting",
          duration = Config.Airdrop.LootProgressDuration,
          position = 'bottom',
          canCancel = true,
          disable = {
              move = true,
              combat = true,
          },
          anim = {
              dict = Config.Animations.Looting.Dict,
              clip = Config.Animations.Looting.Clip,
              flag = Config.Animations.Looting.Flag,
              blendIn = Config.Animations.Looting.BlendIn
          },
        }) 
        then
          TriggerServerEvent('GM_Airdrops:server:getLoot')
        else
          TriggerServerEvent('GM_Airdrops:server:cancelLoot')
        end

      else
        QBCore.Functions.Notify("This was already looted or beeing looted", "error")
      end
    end
  }})
end

RegisterNetEvent('GM_Airdrops:client:callAirDrop',function(item)
  if lib.progressCircle({
    label = 'Calling The Russians',
    duration = 5000,
    position = 'bottom',
    useWhileDead = false,
    canCancel = true,
    disable = {
        car = true,
    },
    anim = {
        dict = Config.Animations.Calling.Dict,
        clip = Config.Animations.Calling.Clip
    },
    prop = {
        model = GetHashKey(Config.Animations.Calling.Prop.Model),
        pos = vec3(Config.Animations.Calling.Prop.Position.x, Config.Animations.Calling.Prop.Position.y, Config.Animations.Calling.Prop.Position.z),
        rot = vec3(Config.Animations.Calling.Prop.Rotation.x, Config.Animations.Calling.Prop.Rotation.y, Config.Animations.Calling.Prop.Rotation.z)
    },
}) then TriggerServerEvent("GM_Airdrops:server:callAirDrop", item) end
end)


RegisterNetEvent('GM_Airdrops:client:clearStuff',function()
  hasNotified = false
  StopParticleFxLooped(effect, 0)
  DeleteEntity(drop)
  DeleteEntity(Pilot)
  DeleteEntity(Plane)
  RemoveBlip(airdropBlip)
  RemoveBlip(radius)
  AirDropZone:destroy()
  exports.ox_target:removeLocalEntity(drop)
end)

RegisterNetEvent('GM_Airdrops:client:endZone',function()
  QBCore.Functions.Notify("Airdrop KOS Zone has ended!", 5000)
end)

AddEventHandler('onResourceStop', function(resourceName)
  if (GetCurrentResourceName() ~= resourceName) then
    return
  end
  StopParticleFxLooped(effect, 0)
  DeleteEntity(drop)
  DeleteEntity(Pilot)
  DeleteEntity(Plane)
  RemoveBlip(airdropBlip)
  RemoveBlip(radius)
  exports.ox_target:removeLocalEntity(drop)
end)