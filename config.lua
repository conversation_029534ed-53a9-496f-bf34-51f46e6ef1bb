Config = {}

-- Airdrop Settings
Config.Airdrop = {
    Cooldown = 7200, -- Cooldown in seconds (2 hours)
    MinPlayers = 0, -- Minimum players required
    RequiredItem = "xd_satellite_phone", -- Item needed to call airdrop
    LootProgressDuration = 60000, -- Duration of looting progress bar (60000)
    PlaneSpawnDelay = 600000, -- Delay before plane spawns (10 minutes - 600000)
    BoxDespawnTime = 300000, -- Time until box despawns (5 minutes - 300000)
    ItemsPerLoot = {min = 1, max = 2}, -- Random number of items per loot (min to max)
    CrateModel = "prop_drop_armscrate_01", -- Model for the airdrop crate
    HaltVehilce = false, -- Whether to halt the vehicle if someone is driving in the zone
}

-- Aircraft Configuration
Config.Aircraft = {
    Models = {
        Pilot = "s_m_m_pilot_01",
        Vehicle = "titan"
    },
    Settings = {
        Height = 450.0,
        Speed = 92.0,
        SpawnPoint = vec3(3562.5, 1356.43, 450.0)
    }
}

-- Zone Settings
Config.Zone = {
    Radius = 250.0,
    BlipColor = 1,
    BlipAlpha = 80,
    BlipSprite = 550
}

-- Proximity Notification Settings
Config.ProximityNotifications = {
    Enabled = true,
    Messages = {
        Nearby = "You are near an active airdrop! The crate contains valuable loot. Other players may be nearby",
        Leaving = "You are leaving the airdrop zone."
    }
}

-- Animation Settings
Config.Animations = {
    Calling = {
        Dict = "cellphone@",
        Clip = "cellphone_call_listen_base",
        Flag = 1,
        Prop = {
            Model = "prop_amb_phone",
            Position = vector3(0.03, 0.03, 0.02),
            Rotation = vector3(0.0, 0.0, -1.5)
        }
    },
    Looting = {
        Dict = "missexile3",
        Clip = "ex03_dingy_search_case_base_michael",
        Flag = 1,
        BlendIn = 1.0
    }
}

-- Drop Locations
Config.Locations = {
    vec3(1951.476, 4960.003, 43.707), -- Sandy Farm
    vec3(1556.62, 3554.574, 35.363), -- Sandy Hotel
    vec3(-2406.428, 3304.266, 32.83), -- Milletry Base
    vec3(2551.086, -377.82, 92.993), -- Gov Facility
    vec3(199.168, -935.164, 30.687), -- Legion Square
    vec3(-117.549, -1049.313, 27.274), -- Construction
    vec3(326.89, -2033.167, 20.948), -- Groove Vagos house
    vec3(105.836, -1940.399, 20.804), -- Grove Street Ballas
    vec3(-181.035, -1601.364, 34.225), -- ForumDrive GSF
    vec3(-1176.417, -1490.123, 4.38), -- Vesspuchi Beach
    vec3(1364.425, -580.187, 74.38), -- Nikola
}

-- Loot Configuration
Config.LootTable = {
    {
        item = 'WEAPON_AK47',
        amount = 1,
    },
    {
        item = 'WEAPON_HK416',
        amount = 1,
    },
    {
        item = 'WEAPON_MP5',
        amount = 1,
    },
    {
        item = 'WEAPON_GLOCK18C',
        amount = 1,
    },
    {
        item = 'WEAPON_REMINGTON',
        amount = 1,
    },
}